// Trading Panel Types
export interface TradingPanelConfig {
  id: string;
  name: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

// Trade Data Types (migrated from frontend)
export interface Trade {
  id?: string;
  timestamp?: number;
  date?: number; // Mobula API might use 'date' field
  type: 'buy' | 'sell';
  token_price?: number;
  token_amount?: number;
  token_amount_vs?: number; // SOL amount in trades
  token_amount_usd?: number;
  hash?: string;
  transaction_hash?: string;
  tx_hash?: string;
  sender?: string;
  pairData?: {
    price?: number;
    token0?: {
      address?: string;
      price?: number;
      marketCap?: number;
      totalSupply?: number;
      symbol?: string;
    };
    token1?: {
      address?: string;
      price?: number;
      marketCap?: number;
      totalSupply?: number;
      symbol?: string;
    };
    liquidity?: number;
    // Volume metrics for different timeframes
    volume24h?: number;
    volume_5min?: number;
    volume_1h?: number;
    volume_6h?: number;
    // Buy/Sell transaction counts
    buyers_24h?: number;
    sellers_24h?: number;
    // Buy/Sell volume amounts
    buy_volume_5min?: number;
    sell_volume_5min?: number;
    buy_volume_1h?: number;
    sell_volume_1h?: number;
    buy_volume_6h?: number;
    sell_volume_6h?: number;
    buy_volume_24h?: number;
    sell_volume_24h?: number;
    // Net volume calculations
    net_volume_5min?: number;
    net_volume_1h?: number;
    net_volume_6h?: number;
    net_volume_24h?: number;
  };
  // Additional fields from Mobula API
  marketCap?: number;
  amount?: number;
  usd_amount?: number;
  value_usd?: number;
  price?: number;
  symbol?: string;
  blockchain?: string;
  chain?: string;
}

export interface FormattedTrade {
  id: string;
  timestamp: number;
  type: 'buy' | 'sell';
  amount: string;
  usdAmount: string;
  mc: string;
  price: string;
  trader: string;
  age: string;
  txHash: string;
  marketCap: number;
  tokenAmount: number; // SOL amount (token_amount_vs) - used for side panel
  tokenAmountUsd: number;
  actualTokenAmount: number; // Actual token quantity (token_amount) - used for bottom table
  // Additional fields for live data
  pairData?: {
    price?: number;
    token0?: {
      address?: string;
      price?: number;
      marketCap?: number;
      totalSupply?: number;
      symbol?: string;
    };
    token1?: {
      address?: string;
      price?: number;
      marketCap?: number;
      totalSupply?: number;
      symbol?: string;
    };
    liquidity?: number;
    // Volume metrics for different timeframes
    volume24h?: number;
    volume_5min?: number;
    volume_1h?: number;
    volume_6h?: number;
    // Buy/Sell transaction counts
    buyers_24h?: number;
    sellers_24h?: number;
    // Buy/Sell volume amounts
    buy_volume_5min?: number;
    sell_volume_5min?: number;
    buy_volume_1h?: number;
    sell_volume_1h?: number;
    buy_volume_6h?: number;
    sell_volume_6h?: number;
    buy_volume_24h?: number;
    sell_volume_24h?: number;
    // Net volume calculations
    net_volume_5min?: number;
    net_volume_1h?: number;
    net_volume_6h?: number;
    net_volume_24h?: number;
  };
  token_price?: number;
  // Allow any additional fields from original trade
  [key: string]: any;
}

// WebSocket Message Types
export interface WebSocketMessage {
  type: 'trade' | 'error' | 'connected' | 'disconnected';
  data?: any;
  error?: string;
}

export interface SubscriptionRequest {
  action: 'subscribe' | 'unsubscribe';
  poolAddress: string;
}
