import { useState, useEffect } from 'react';
import { useBackendTradeData } from '@/hooks/useBackendTradeData';

interface TimeframeData {
  timeframe: string;
  volume: string;
  buyVolume: number;
  sellVolume: number;
  buyCount: number;
  sellCount: number;
  netVolume: string;
}

interface AllTimeframeStats {
  timeframes: TimeframeData[];
  selectedTimeframe: string;
}

const timeframes = ['5m', '1h', '6h', '24h'];

export const TradingStats = () => {
  const [selectedTimeframe, setSelectedTimeframe] = useState('24h');
  const [allTimeframeStats, setAllTimeframeStats] = useState<TimeframeData[]>([]);

  // Get pool address from localStorage for WebSocket connection
  const getPoolAddressFromStorage = (): string | null => {
    try {
      const activePulseTokenStr = localStorage.getItem('activePulseToken');
      if (activePulseTokenStr) {
        const activePulseToken = JSON.parse(activePulseTokenStr);
        return activePulseToken.pool_address || null;
      }
    } catch (error) {
      console.error('Error parsing activePulseToken from localStorage:', error);
    }
    return null;
  };

  const poolAddress = getPoolAddressFromStorage();
  const { latestRawTrade, isConnected } = useBackendTradeData(poolAddress);

  // Format numbers with K/M/B suffixes
  const formatVolume = (value: number): string => {
    if (value === 0) return '$0';
    if (value >= 1_000_000_000) return `$${(value / 1_000_000_000).toFixed(2)}B`;
    if (value >= 1_000_000) return `$${(value / 1_000_000).toFixed(2)}M`;
    if (value >= 1_000) return `$${(value / 1_000).toFixed(2)}K`;
    return `$${value.toFixed(2)}`;
  };

  // Calculate stats for all timeframes from the latest trade data
  useEffect(() => {
    if (latestRawTrade && isConnected && latestRawTrade.pairData) {
      const pairData = latestRawTrade.pairData;

      // Calculate data for all timeframes
      const timeframeStats: TimeframeData[] = timeframes.map(tf => {
        let volume = 0;
        let buyVolume = 0;
        let sellVolume = 0;
        let buyCount = 0;
        let sellCount = 0;

        switch (tf) {
          case '5m':
            volume = pairData.volume_5min || 0;
            buyVolume = pairData.buy_volume_5min || 0;
            sellVolume = pairData.sell_volume_5min || 0;
            buyCount = pairData.buyers_5min || 0;
            sellCount = pairData.sellers_5min || 0;
            break;
          case '1h':
            volume = pairData.volume_1h || 0;
            buyVolume = pairData.buy_volume_1h || 0;
            sellVolume = pairData.sell_volume_1h || 0;
            buyCount = pairData.buyers_1h || 0;
            sellCount = pairData.sellers_1h || 0;
            break;
          case '6h':
            volume = pairData.volume_6h || 0;
            buyVolume = pairData.buy_volume_6h || 0;
            sellVolume = pairData.sell_volume_6h || 0;
            buyCount = pairData.buyers_6h || 0;
            sellCount = pairData.sellers_6h || 0;
            break;
          case '24h':
            volume = pairData.volume24h || 0;
            buyVolume = pairData.buy_volume_24h || 0;
            sellVolume = pairData.sell_volume_24h || 0;
            buyCount = pairData.buyers_24h || 0;
            sellCount = pairData.sellers_24h || 0;
            break;
        }

        const netVolume = buyVolume - sellVolume;

        return {
          timeframe: tf,
          volume: formatVolume(volume),
          buyVolume,
          sellVolume,
          buyCount,
          sellCount,
          netVolume: formatVolume(Math.abs(netVolume))
        };
      });

      setAllTimeframeStats(timeframeStats);

      console.log('📊 [TRADING STATS] Updated all timeframes with Mobula data:', {
        timeframeStats,
        availableMobulaFields: Object.keys(pairData).filter(key => key.includes('volume') || key.includes('buy') || key.includes('sell'))
      });
    } else {
      // No data available, show empty stats
      setAllTimeframeStats([]);
    }
  }, [latestRawTrade, isConnected]);

  // Get selected timeframe data for detailed stats
  const selectedData = allTimeframeStats.find(tf => tf.timeframe === selectedTimeframe);
  const totalAmount = selectedData ? (selectedData.buyVolume + selectedData.sellVolume) || 1 : 1;
  const buyPercent = selectedData ? (selectedData.buyVolume / totalAmount) * 100 : 0;
  const sellPercent = selectedData ? (selectedData.sellVolume / totalAmount) * 100 : 0;

  return (
    <div className="text-white border border-[#2a2a2e] h-full flex flex-col">
      {/* Top Section: All Timeframes with Volumes Always Visible */}
      <div className="grid grid-cols-4 gap-1 p-3 border-b border-[#2a2a2e]">
        {timeframes.map((tf) => {
          const tfData = allTimeframeStats.find(data => data.timeframe === tf);
          const isSelected = selectedTimeframe === tf;

          return (
            <button
              key={tf}
              onClick={() => setSelectedTimeframe(tf)}
              className={`p-2 rounded transition-colors flex flex-col items-center justify-center min-h-[60px] ${
                isSelected ? 'bg-[#1a1a1a] border border-[#6683FF]' : 'bg-transparent hover:bg-[#2a2a2e]'
              }`}
            >
              <div className="text-gray-300 text-xs font-medium mb-1">{tf}</div>
              <div className={`text-xs font-semibold ${isSelected ? 'text-[#6683FF]' : 'text-white'}`}>
                {tfData ? tfData.volume : '$0'}
              </div>
            </button>
          );
        })}
      </div>

      {/* Bottom Section: Detailed Stats for Selected Timeframe */}
      <div className="flex-1 px-4 py-3 flex flex-col justify-between">
        <div className="grid grid-cols-4 gap-3 text-sm">
          <div className="text-center">
            <div className="text-gray-400 text-xs mb-1">{selectedTimeframe} Vol</div>
            <div className="text-white font-medium">
              {selectedData ? selectedData.volume : '$0'}
            </div>
          </div>
          <div className="text-center">
            <div className="text-gray-400 text-xs mb-1">Buys</div>
            <div className="text-green-400 font-medium text-xs">
              {selectedData ? `${selectedData.buyCount} / $${Math.round(selectedData.buyVolume)}` : '0 / $0'}
            </div>
          </div>
          <div className="text-center">
            <div className="text-gray-400 text-xs mb-1">Sells</div>
            <div className="text-pink-400 font-medium text-xs">
              {selectedData ? `${selectedData.sellCount} / $${Math.round(selectedData.sellVolume)}` : '0 / $0'}
            </div>
          </div>
          <div className="text-center">
            <div className="text-gray-400 text-xs mb-1">Net Vol.</div>
            <div className="text-pink-400 font-medium">
              {selectedData ? selectedData.netVolume : '$0'}
            </div>
          </div>
        </div>

        {/* Buy/Sell Volume Bar */}
        <div className="flex w-full mt-3 h-1 rounded overflow-hidden bg-[#333]">
          <div
            className="bg-green-500 h-full transition-all duration-300"
            style={{ width: `${buyPercent}%` }}
          />
          <div
            className="bg-pink-500 h-full transition-all duration-300"
            style={{ width: `${sellPercent}%` }}
          />
        </div>
      </div>
    </div>
  );
};
